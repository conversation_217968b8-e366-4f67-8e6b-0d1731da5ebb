// Utility functions for FleetXQ application

/**
 * Format date to readable string
 */
export const formatDate = (date: string | Date): string => {
  const d = new Date(date);
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

/**
 * Format distance in miles
 */
export const formatDistance = (miles: number): string => {
  if (miles < 1) {
    return `${(miles * 5280).toFixed(0)} ft`;
  }
  return `${miles.toFixed(1)} mi`;
};

/**
 * Format speed in MPH
 */
export const formatSpeed = (mph: number): string => {
  return `${mph.toFixed(1)} mph`;
};

/**
 * Format fuel level as percentage
 */
export const formatFuelLevel = (level: number): string => {
  return `${(level * 100).toFixed(0)}%`;
};

/**
 * Get status color class based on status
 */
export const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'active':
    case 'on':
    case 'online':
      return 'text-success-600 bg-success-100';
    case 'inactive':
    case 'off':
    case 'offline':
      return 'text-gray-600 bg-gray-100';
    case 'maintenance':
    case 'warning':
      return 'text-warning-600 bg-warning-100';
    case 'critical':
    case 'danger':
    case 'error':
      return 'text-danger-600 bg-danger-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

/**
 * Debounce function for search inputs
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout>;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Generate random ID
 */
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

/**
 * Capitalize first letter of string
 */
export const capitalize = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * Truncate text to specified length
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substr(0, maxLength) + '...';
};
