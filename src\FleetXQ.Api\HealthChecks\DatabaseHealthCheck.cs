using Microsoft.Extensions.Diagnostics.HealthChecks;
using FleetXQ.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics;

namespace FleetXQ.Api.HealthChecks;

/// <summary>
/// Health check for database connectivity and performance
/// </summary>
public class DatabaseHealthCheck : IHealthCheck
{
    private readonly FleetXQDbContext _dbContext;
    private readonly ILogger<DatabaseHealthCheck> _logger;

    public DatabaseHealthCheck(FleetXQDbContext dbContext, ILogger<DatabaseHealthCheck> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var stopwatch = Stopwatch.StartNew();
            
            // Test basic connectivity
            var canConnect = await _dbContext.Database.CanConnectAsync(cancellationToken);
            if (!canConnect)
            {
                return HealthCheckResult.Unhealthy("Cannot connect to database");
            }

            // Test query performance
            var vehicleCount = await _dbContext.Vehicles.CountAsync(cancellationToken);
            var telemetryCount = await _dbContext.TelemetryData.CountAsync(cancellationToken);
            var alertCount = await _dbContext.Alerts.CountAsync(cancellationToken);
            
            stopwatch.Stop();
            var responseTime = stopwatch.ElapsedMilliseconds;

            var data = new Dictionary<string, object>
            {
                ["ResponseTimeMs"] = responseTime,
                ["VehicleCount"] = vehicleCount,
                ["TelemetryCount"] = telemetryCount,
                ["AlertCount"] = alertCount,
                ["ConnectionString"] = _dbContext.Database.GetConnectionString()?.Substring(0, 50) + "...",
                ["DatabaseProvider"] = _dbContext.Database.ProviderName
            };

            // Determine health status based on response time
            if (responseTime > 5000) // 5 seconds
            {
                return HealthCheckResult.Unhealthy("Database response time is too slow", null, data);
            }
            else if (responseTime > 2000) // 2 seconds
            {
                return HealthCheckResult.Degraded("Database response time is slow", null, data);
            }

            return HealthCheckResult.Healthy("Database is healthy", data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database health check failed");
            return HealthCheckResult.Unhealthy("Database health check failed", ex);
        }
    }
}

/// <summary>
/// Health check for SignalR hubs
/// </summary>
public class SignalRHealthCheck : IHealthCheck
{
    private readonly ILogger<SignalRHealthCheck> _logger;
    private readonly IServiceProvider _serviceProvider;

    public SignalRHealthCheck(ILogger<SignalRHealthCheck> logger, IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if SignalR services are registered and available
            var hubContext = _serviceProvider.GetService<Microsoft.AspNetCore.SignalR.IHubContext<FleetXQ.Api.Hubs.TelemetryHub>>();
            var alertHubContext = _serviceProvider.GetService<Microsoft.AspNetCore.SignalR.IHubContext<FleetXQ.Api.Hubs.AlertHub>>();
            var dashboardHubContext = _serviceProvider.GetService<Microsoft.AspNetCore.SignalR.IHubContext<FleetXQ.Api.Hubs.DashboardHub>>();

            var data = new Dictionary<string, object>
            {
                ["TelemetryHubAvailable"] = hubContext != null,
                ["AlertHubAvailable"] = alertHubContext != null,
                ["DashboardHubAvailable"] = dashboardHubContext != null,
                ["CheckTime"] = DateTime.UtcNow
            };

            if (hubContext == null || alertHubContext == null || dashboardHubContext == null)
            {
                return HealthCheckResult.Unhealthy("One or more SignalR hubs are not available", null, data);
            }

            return HealthCheckResult.Healthy("All SignalR hubs are available", data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "SignalR health check failed");
            return HealthCheckResult.Unhealthy("SignalR health check failed", ex);
        }
    }
}

/// <summary>
/// Health check for system resources
/// </summary>
public class SystemResourcesHealthCheck : IHealthCheck
{
    private readonly ILogger<SystemResourcesHealthCheck> _logger;

    public SystemResourcesHealthCheck(ILogger<SystemResourcesHealthCheck> logger)
    {
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var process = Process.GetCurrentProcess();
            
            // Memory metrics
            var workingSet = process.WorkingSet64;
            var privateMemory = process.PrivateMemorySize64;
            var gcMemory = GC.GetTotalMemory(false);
            
            // Convert to MB for easier reading
            var workingSetMB = workingSet / (1024 * 1024);
            var privateMemoryMB = privateMemory / (1024 * 1024);
            var gcMemoryMB = gcMemory / (1024 * 1024);

            // Thread and handle metrics
            var threadCount = process.Threads.Count;
            var handleCount = process.HandleCount;

            // GC metrics
            var gen0Collections = GC.CollectionCount(0);
            var gen1Collections = GC.CollectionCount(1);
            var gen2Collections = GC.CollectionCount(2);

            var data = new Dictionary<string, object>
            {
                ["WorkingSetMB"] = workingSetMB,
                ["PrivateMemoryMB"] = privateMemoryMB,
                ["GCMemoryMB"] = gcMemoryMB,
                ["ThreadCount"] = threadCount,
                ["HandleCount"] = handleCount,
                ["GCGen0Collections"] = gen0Collections,
                ["GCGen1Collections"] = gen1Collections,
                ["GCGen2Collections"] = gen2Collections,
                ["ProcessorCount"] = Environment.ProcessorCount,
                ["MachineName"] = Environment.MachineName,
                ["OSVersion"] = Environment.OSVersion.ToString(),
                ["CheckTime"] = DateTime.UtcNow
            };

            // Determine health status based on resource usage
            var issues = new List<string>();

            if (workingSetMB > 1000) // 1GB
            {
                issues.Add($"High working set memory usage: {workingSetMB}MB");
            }

            if (threadCount > 100)
            {
                issues.Add($"High thread count: {threadCount}");
            }

            if (gcMemoryMB > 500) // 500MB
            {
                issues.Add($"High GC memory usage: {gcMemoryMB}MB");
            }

            if (issues.Any())
            {
                var issueMessage = string.Join("; ", issues);
                if (workingSetMB > 2000 || threadCount > 200) // Critical thresholds
                {
                    return HealthCheckResult.Unhealthy($"Critical resource usage: {issueMessage}", null, data);
                }
                else
                {
                    return HealthCheckResult.Degraded($"High resource usage: {issueMessage}", null, data);
                }
            }

            return HealthCheckResult.Healthy("System resources are healthy", data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "System resources health check failed");
            return HealthCheckResult.Unhealthy("System resources health check failed", ex);
        }
    }
}

/// <summary>
/// Health check for application-specific services
/// </summary>
public class ApplicationServicesHealthCheck : IHealthCheck
{
    private readonly ILogger<ApplicationServicesHealthCheck> _logger;
    private readonly IServiceProvider _serviceProvider;

    public ApplicationServicesHealthCheck(ILogger<ApplicationServicesHealthCheck> logger, IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var data = new Dictionary<string, object>
            {
                ["CheckTime"] = DateTime.UtcNow
            };

            var issues = new List<string>();

            // Check MediatR
            var mediator = _serviceProvider.GetService<MediatR.IMediator>();
            data["MediatRAvailable"] = mediator != null;
            if (mediator == null)
            {
                issues.Add("MediatR service not available");
            }

            // Check custom services
            var telemetryService = _serviceProvider.GetService<FleetXQ.Api.Services.CustomTelemetryService>();
            data["CustomTelemetryServiceAvailable"] = telemetryService != null;
            if (telemetryService == null)
            {
                issues.Add("Custom telemetry service not available");
            }

            var signalRLoggingService = _serviceProvider.GetService<FleetXQ.Api.Services.SignalRLoggingService>();
            data["SignalRLoggingServiceAvailable"] = signalRLoggingService != null;
            if (signalRLoggingService == null)
            {
                issues.Add("SignalR logging service not available");
            }

            // Check Application Insights
            var telemetryClient = _serviceProvider.GetService<Microsoft.ApplicationInsights.TelemetryClient>();
            data["ApplicationInsightsAvailable"] = telemetryClient != null;
            if (telemetryClient == null)
            {
                issues.Add("Application Insights telemetry client not available");
            }

            if (issues.Any())
            {
                var issueMessage = string.Join("; ", issues);
                return HealthCheckResult.Unhealthy($"Application services issues: {issueMessage}", null, data);
            }

            return HealthCheckResult.Healthy("All application services are available", data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Application services health check failed");
            return HealthCheckResult.Unhealthy("Application services health check failed", ex);
        }
    }
}
