import { createBrowserRouter, Navigate } from 'react-router-dom';
import { lazy, Suspense } from 'react';
import MainLayout from '../layouts/MainLayout';
import AuthLayout from '../layouts/AuthLayout';
import ProtectedRoute from './ProtectedRoute';
import LoadingSpinner from '../components/LoadingSpinner';

// Lazy load components for code splitting
const LoginPage = lazy(() => import('../features/auth/LoginPage'));
const DashboardPage = lazy(() => import('../features/dashboard/DashboardPage'));
const VehiclesPage = lazy(() => import('../features/vehicles/VehiclesPage'));
const DriversPage = lazy(() => import('../features/drivers/DriversPage'));
const TelemetryPage = lazy(() => import('../features/telemetry/TelemetryPage'));
const AlertsPage = lazy(() => import('../features/alerts/AlertsPage'));

// Wrapper component for lazy loaded components
const LazyWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<LoadingSpinner />}>{children}</Suspense>
);

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to='/dashboard' replace />,
  },
  {
    path: '/auth',
    element: <AuthLayout />,
    children: [
      {
        path: 'login',
        element: (
          <LazyWrapper>
            <LoginPage />
          </LazyWrapper>
        ),
      },
      {
        index: true,
        element: <Navigate to='/auth/login' replace />,
      },
    ],
  },
  {
    path: '/',
    element: (
      <ProtectedRoute>
        <MainLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        path: 'dashboard',
        element: (
          <LazyWrapper>
            <DashboardPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'vehicles',
        element: (
          <LazyWrapper>
            <VehiclesPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'drivers',
        element: (
          <LazyWrapper>
            <DriversPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'telemetry',
        element: (
          <LazyWrapper>
            <TelemetryPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'alerts',
        element: (
          <LazyWrapper>
            <AlertsPage />
          </LazyWrapper>
        ),
      },
    ],
  },
  {
    path: '*',
    element: <Navigate to='/dashboard' replace />,
  },
]);

export default router;
