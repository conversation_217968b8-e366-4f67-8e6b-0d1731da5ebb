// Common types for FleetXQ application

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'manager' | 'driver';
  isAuthenticated: boolean;
}

export interface Vehicle {
  id: string;
  make: string;
  model: string;
  year: number;
  licensePlate: string;
  vin: string;
  status: 'active' | 'maintenance' | 'inactive';
  driverId?: string;
}

export interface Driver {
  id: string;
  name: string;
  email: string;
  phone: string;
  licenseNumber: string;
  status: 'active' | 'inactive';
  vehicleId?: string;
}

export interface TelemetryData {
  vehicleId: string;
  timestamp: string;
  location: {
    latitude: number;
    longitude: number;
  };
  speed: number;
  fuelLevel: number;
  engineStatus: 'on' | 'off';
  temperature: number;
}

export interface Alert {
  id: string;
  vehicleId: string;
  type: 'maintenance' | 'speed' | 'fuel' | 'location' | 'engine';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  acknowledged: boolean;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
