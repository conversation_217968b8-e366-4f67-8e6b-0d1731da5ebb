import { NavLink } from 'react-router-dom';
import {
  HomeIcon,
  TruckIcon,
  UserGroupIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'Vehicles', href: '/vehicles', icon: TruckIcon },
  { name: 'Drivers', href: '/drivers', icon: UserGroupIcon },
  { name: 'Telemetry', href: '/telemetry', icon: ChartBarIcon },
  { name: 'Alerts', href: '/alerts', icon: ExclamationTriangleIcon },
];

const Sidebar = () => {
  return (
    <div className='flex w-64 flex-col bg-white shadow-sm'>
      {/* Logo */}
      <div className='flex h-16 items-center justify-center border-b border-gray-200 px-4'>
        <h1 className='text-primary-600 text-xl font-bold'>FleetXQ</h1>
      </div>

      {/* Navigation */}
      <nav className='flex-1 space-y-1 px-4 py-6'>
        {navigation.map(item => (
          <NavLink
            key={item.name}
            to={item.href}
            className={({ isActive }) =>
              `sidebar-nav-item ${
                isActive
                  ? 'sidebar-nav-item-active'
                  : 'sidebar-nav-item-inactive'
              }`
            }
          >
            <item.icon className='mr-3 h-5 w-5' />
            {item.name}
          </NavLink>
        ))}
      </nav>
    </div>
  );
};

export default Sidebar;
