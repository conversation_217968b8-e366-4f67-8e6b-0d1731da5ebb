using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.AspNetCore.Http;
using Serilog.Context;
using System.Diagnostics;
using System.Security.Claims;

namespace FleetXQ.Application.Common.Behaviors;

/// <summary>
/// Pipeline behavior for performance monitoring and alerting
/// </summary>
/// <typeparam name="TRequest">The request type</typeparam>
/// <typeparam name="TResponse">The response type</typeparam>
public sealed class PerformanceBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    private readonly ILogger<PerformanceBehavior<TRequest, TResponse>> _logger;
    private readonly TelemetryClient _telemetryClient;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly long _slowRequestThresholdMs;
    private readonly long _criticalRequestThresholdMs;

    /// <summary>
    /// Initializes a new instance of the <see cref="PerformanceBehavior{TRequest, TResponse}"/> class
    /// </summary>
    /// <param name="logger">The logger</param>
    /// <param name="telemetryClient">The Application Insights telemetry client</param>
    /// <param name="httpContextAccessor">The HTTP context accessor</param>
    /// <param name="slowRequestThresholdMs">The threshold in milliseconds for slow requests (default: 500ms)</param>
    /// <param name="criticalRequestThresholdMs">The threshold in milliseconds for critical slow requests (default: 2000ms)</param>
    public PerformanceBehavior(
        ILogger<PerformanceBehavior<TRequest, TResponse>> logger,
        TelemetryClient telemetryClient,
        IHttpContextAccessor httpContextAccessor,
        long slowRequestThresholdMs = 500,
        long criticalRequestThresholdMs = 2000)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _telemetryClient = telemetryClient ?? throw new ArgumentNullException(nameof(telemetryClient));
        _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        _slowRequestThresholdMs = slowRequestThresholdMs;
        _criticalRequestThresholdMs = criticalRequestThresholdMs;
    }

    /// <summary>
    /// Handles the request performance monitoring
    /// </summary>
    /// <param name="request">The request</param>
    /// <param name="next">The next handler in the pipeline</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The response</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var requestId = Guid.NewGuid();
        var correlationId = GetCorrelationId();
        var userContext = GetUserContext();
        var stopwatch = Stopwatch.StartNew();
        var memoryBefore = GC.GetTotalMemory(false);
        var requestStartTime = DateTime.UtcNow;

        // Get initial system metrics
        var initialCpuTime = Process.GetCurrentProcess().TotalProcessorTime;
        var initialThreadCount = Process.GetCurrentProcess().Threads.Count;

        using (LogContext.PushProperty("RequestId", requestId))
        using (LogContext.PushProperty("CorrelationId", correlationId))
        using (LogContext.PushProperty("RequestName", requestName))
        using (LogContext.PushProperty("UserId", userContext.UserId))
        {
            try
            {
                var response = await next();

                stopwatch.Stop();
                var memoryAfter = GC.GetTotalMemory(false);
                var memoryUsed = memoryAfter - memoryBefore;
                var finalCpuTime = Process.GetCurrentProcess().TotalProcessorTime;
                var cpuUsed = (finalCpuTime - initialCpuTime).TotalMilliseconds;
                var finalThreadCount = Process.GetCurrentProcess().Threads.Count;

                await LogPerformanceMetricsAsync(requestName, requestId, correlationId, userContext,
                    stopwatch.ElapsedMilliseconds, memoryUsed, cpuUsed, initialThreadCount, finalThreadCount,
                    requestStartTime, true, null);

                return response;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var memoryAfter = GC.GetTotalMemory(false);
                var memoryUsed = memoryAfter - memoryBefore;
                var finalCpuTime = Process.GetCurrentProcess().TotalProcessorTime;
                var cpuUsed = (finalCpuTime - initialCpuTime).TotalMilliseconds;
                var finalThreadCount = Process.GetCurrentProcess().Threads.Count;

                await LogPerformanceMetricsAsync(requestName, requestId, correlationId, userContext,
                    stopwatch.ElapsedMilliseconds, memoryUsed, cpuUsed, initialThreadCount, finalThreadCount,
                    requestStartTime, false, ex);

                // Re-throw the exception
                throw;
            }
        }
    }

    /// <summary>
    /// Gets the correlation ID from the current HTTP context
    /// </summary>
    private string GetCorrelationId()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext?.Request.Headers.TryGetValue("X-Correlation-ID", out var correlationId) == true)
        {
            return correlationId.FirstOrDefault() ?? Guid.NewGuid().ToString();
        }
        return Guid.NewGuid().ToString();
    }

    /// <summary>
    /// Gets the user context from the current HTTP context
    /// </summary>
    private UserContext GetUserContext()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        var user = httpContext?.User;

        return new UserContext
        {
            UserId = user?.FindFirst(ClaimTypes.NameIdentifier)?.Value,
            UserName = user?.FindFirst(ClaimTypes.Name)?.Value ?? user?.FindFirst("name")?.Value,
            IsAuthenticated = user?.Identity?.IsAuthenticated ?? false
        };
    }

    /// <summary>
    /// Logs comprehensive performance metrics for the request
    /// </summary>
    private async Task LogPerformanceMetricsAsync(string requestName, Guid requestId, string correlationId,
        UserContext userContext, long elapsedMs, long memoryUsed, double cpuUsed, int initialThreadCount,
        int finalThreadCount, DateTime requestStartTime, bool success, Exception? exception)
    {
        var logLevel = DetermineLogLevel(elapsedMs, success, exception);
        var status = success ? "completed" : "failed";

        _logger.Log(logLevel,
            "Performance: {RequestName} {Status} in {ElapsedMs}ms, Memory: {MemoryUsedKB}KB, CPU: {CpuUsedMs}ms, Threads: {ThreadCount}",
            requestName, status, elapsedMs, memoryUsed / 1024, cpuUsed, finalThreadCount);

        // Create comprehensive performance telemetry
        var performanceTelemetry = new DependencyTelemetry
        {
            Name = $"CQRS.{requestName}",
            Type = GetRequestType(requestName),
            Duration = TimeSpan.FromMilliseconds(elapsedMs),
            Success = success,
            Timestamp = requestStartTime
        };

        performanceTelemetry.Properties["RequestId"] = requestId.ToString();
        performanceTelemetry.Properties["CorrelationId"] = correlationId;
        performanceTelemetry.Properties["UserId"] = userContext.UserId ?? "Anonymous";
        performanceTelemetry.Properties["UserName"] = userContext.UserName ?? "Anonymous";
        performanceTelemetry.Properties["MemoryUsedKB"] = (memoryUsed / 1024).ToString();
        performanceTelemetry.Properties["CpuUsedMs"] = cpuUsed.ToString("F2");
        performanceTelemetry.Properties["ThreadCount"] = finalThreadCount.ToString();
        performanceTelemetry.Properties["ThreadDelta"] = (finalThreadCount - initialThreadCount).ToString();

        if (exception != null)
        {
            performanceTelemetry.Properties["ExceptionType"] = exception.GetType().Name;
            performanceTelemetry.Properties["ExceptionMessage"] = exception.Message;
        }

        _telemetryClient.TrackDependency(performanceTelemetry);

        // Track detailed metrics
        _telemetryClient.TrackMetric($"Performance.{requestName}.Duration", elapsedMs);
        _telemetryClient.TrackMetric($"Performance.{requestName}.MemoryUsage", memoryUsed);
        _telemetryClient.TrackMetric($"Performance.{requestName}.CpuUsage", cpuUsed);
        _telemetryClient.TrackMetric($"Performance.{requestName}.ThreadCount", finalThreadCount);

        // Log and track slow requests
        if (elapsedMs > _slowRequestThresholdMs)
        {
            await LogSlowRequestDetailsAsync(requestName, requestId, correlationId, elapsedMs, memoryUsed, cpuUsed, userContext);
        }

        // Log and track high memory usage
        if (memoryUsed > 10 * 1024 * 1024) // 10MB threshold
        {
            await LogHighMemoryUsageAsync(requestName, requestId, correlationId, memoryUsed, userContext);
        }

        // Track performance buckets
        var performanceBucket = GetPerformanceBucket(elapsedMs);
        _telemetryClient.TrackMetric($"Performance.{requestName}.{performanceBucket}", 1);
    }

    /// <summary>
    /// Logs additional details for slow requests
    /// </summary>
    /// <param name="requestName">The request name</param>
    /// <param name="elapsedMs">The elapsed time in milliseconds</param>
    /// <param name="memoryUsed">The memory used in bytes</param>
    private void LogSlowRequestDetails(string requestName, long elapsedMs, long memoryUsed)
    {
        // Get current system information
        var process = Process.GetCurrentProcess();
        var workingSet = process.WorkingSet64;
        var threadCount = process.Threads.Count;
        var gcGen0 = GC.CollectionCount(0);
        var gcGen1 = GC.CollectionCount(1);
        var gcGen2 = GC.CollectionCount(2);

        _logger.LogWarning(
            "Slow request system state - Request: {RequestName}, " +
            "WorkingSet: {WorkingSetMB}MB, Threads: {ThreadCount}, " +
            "GC Gen0: {GcGen0}, GC Gen1: {GcGen1}, GC Gen2: {GcGen2}",
            requestName, workingSet / (1024 * 1024), threadCount, gcGen0, gcGen1, gcGen2);

        // In a real application, you might also want to:
        // 1. Capture stack traces
        // 2. Log database connection pool status
        // 3. Check external service response times
        // 4. Monitor CPU usage
        // 5. Send metrics to APM tools (Application Performance Monitoring)
    }

    private LogLevel DetermineLogLevel(long elapsedMs, bool success, Exception? exception)
    {
        if (exception != null)
            return LogLevel.Error;

        if (elapsedMs > _criticalRequestThresholdMs)
            return LogLevel.Error;

        if (elapsedMs > _slowRequestThresholdMs)
            return LogLevel.Warning;

        return LogLevel.Information;
    }

    private string GetRequestType(string requestName)
    {
        return requestName.EndsWith("Command", StringComparison.OrdinalIgnoreCase) ? "Command" : "Query";
    }

    private string GetPerformanceBucket(long elapsedMs)
    {
        return elapsedMs switch
        {
            < 100 => "Fast",
            < 500 => "Normal",
            < 2000 => "Slow",
            _ => "Critical"
        };
    }

    private async Task LogSlowRequestDetailsAsync(string requestName, Guid requestId, string correlationId,
        long elapsedMs, long memoryUsed, double cpuUsed, UserContext userContext)
    {
        // Collect additional system information for slow requests
        var process = Process.GetCurrentProcess();
        var workingSet = process.WorkingSet64;
        var threadCount = process.Threads.Count;

        // Get GC information
        var gcGen0 = GC.CollectionCount(0);
        var gcGen1 = GC.CollectionCount(1);
        var gcGen2 = GC.CollectionCount(2);

        var logLevel = elapsedMs > _criticalRequestThresholdMs ? LogLevel.Error : LogLevel.Warning;

        _logger.Log(logLevel,
            "Slow request detected: {RequestName} ({RequestId}) took {ElapsedMs}ms (threshold: {ThresholdMs}ms) " +
            "for user {UserName} ({UserId}). System state - WorkingSet: {WorkingSetMB}MB, Threads: {ThreadCount}, " +
            "GC Gen0: {GcGen0}, GC Gen1: {GcGen1}, GC Gen2: {GcGen2}, CPU: {CpuUsedMs}ms",
            requestName, requestId, elapsedMs, _slowRequestThresholdMs, userContext.UserName ?? "Anonymous",
            userContext.UserId ?? "Unknown", workingSet / (1024 * 1024), threadCount, gcGen0, gcGen1, gcGen2, cpuUsed);

        // Track slow request event in Application Insights
        var slowRequestEvent = new EventTelemetry("Performance.SlowRequest");
        slowRequestEvent.Properties["RequestName"] = requestName;
        slowRequestEvent.Properties["RequestId"] = requestId.ToString();
        slowRequestEvent.Properties["CorrelationId"] = correlationId;
        slowRequestEvent.Properties["UserId"] = userContext.UserId ?? "Unknown";
        slowRequestEvent.Properties["UserName"] = userContext.UserName ?? "Anonymous";
        slowRequestEvent.Properties["ElapsedMs"] = elapsedMs.ToString();
        slowRequestEvent.Properties["ThresholdMs"] = _slowRequestThresholdMs.ToString();
        slowRequestEvent.Properties["WorkingSetMB"] = (workingSet / (1024 * 1024)).ToString();
        slowRequestEvent.Properties["ThreadCount"] = threadCount.ToString();
        slowRequestEvent.Properties["CpuUsedMs"] = cpuUsed.ToString("F2");
        slowRequestEvent.Metrics["Duration"] = elapsedMs;
        slowRequestEvent.Metrics["MemoryUsed"] = memoryUsed;
        slowRequestEvent.Metrics["CpuUsed"] = cpuUsed;

        _telemetryClient.TrackEvent(slowRequestEvent);
        _telemetryClient.TrackMetric($"Performance.{requestName}.SlowRequests", 1);
    }

    private async Task LogHighMemoryUsageAsync(string requestName, Guid requestId, string correlationId,
        long memoryUsed, UserContext userContext)
    {
        _logger.LogWarning(
            "High memory usage detected: {RequestName} ({RequestId}) used {MemoryUsedMB}MB for user {UserName} ({UserId})",
            requestName, requestId, memoryUsed / (1024 * 1024), userContext.UserName ?? "Anonymous", userContext.UserId ?? "Unknown");

        // Track high memory usage event
        var highMemoryEvent = new EventTelemetry("Performance.HighMemoryUsage");
        highMemoryEvent.Properties["RequestName"] = requestName;
        highMemoryEvent.Properties["RequestId"] = requestId.ToString();
        highMemoryEvent.Properties["CorrelationId"] = correlationId;
        highMemoryEvent.Properties["UserId"] = userContext.UserId ?? "Unknown";
        highMemoryEvent.Properties["UserName"] = userContext.UserName ?? "Anonymous";
        highMemoryEvent.Properties["MemoryUsedMB"] = (memoryUsed / (1024 * 1024)).ToString();
        highMemoryEvent.Metrics["MemoryUsed"] = memoryUsed;

        _telemetryClient.TrackEvent(highMemoryEvent);
        _telemetryClient.TrackMetric($"Performance.{requestName}.HighMemoryUsage", 1);
    }

    private record UserContext
    {
        public string? UserId { get; init; }
        public string? UserName { get; init; }
        public bool IsAuthenticated { get; init; }
    }
}

/// <summary>
/// Performance metrics for a request
/// </summary>
public sealed class PerformanceMetrics
{
    /// <summary>
    /// Gets or sets the request name
    /// </summary>
    public string RequestName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the elapsed time in milliseconds
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// Gets or sets the memory used in bytes
    /// </summary>
    public long MemoryUsed { get; set; }

    /// <summary>
    /// Gets or sets whether the request was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the request was processed
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets additional performance data
    /// </summary>
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}
