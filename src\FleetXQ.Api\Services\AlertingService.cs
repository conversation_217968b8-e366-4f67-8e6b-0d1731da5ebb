using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using System.Collections.Concurrent;

namespace FleetXQ.Api.Services;

/// <summary>
/// Service for detecting critical issues and triggering alerts
/// </summary>
public class AlertingService
{
    private readonly TelemetryClient _telemetryClient;
    private readonly ILogger<AlertingService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ConcurrentDictionary<string, AlertThreshold> _thresholds = new();
    private readonly ConcurrentDictionary<string, AlertState> _alertStates = new();
    private readonly Timer _alertCheckTimer;

    public AlertingService(
        TelemetryClient telemetryClient,
        ILogger<AlertingService> logger,
        IConfiguration configuration)
    {
        _telemetryClient = telemetryClient;
        _logger = logger;
        _configuration = configuration;

        InitializeAlertThresholds();
        
        // Check for alerts every 30 seconds
        _alertCheckTimer = new Timer(CheckAlerts, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
    }

    private void InitializeAlertThresholds()
    {
        // High Error Rate Alert
        _thresholds["HighErrorRate"] = new AlertThreshold
        {
            Name = "High Error Rate",
            Description = "Error rate exceeds acceptable threshold",
            Threshold = _configuration.GetValue("Alerting:ErrorRateThreshold", 5.0), // 5% default
            WindowMinutes = 5,
            Severity = AlertSeverity.High
        };

        // Performance Degradation Alert
        _thresholds["PerformanceDegradation"] = new AlertThreshold
        {
            Name = "Performance Degradation",
            Description = "Average response time exceeds threshold",
            Threshold = _configuration.GetValue("Alerting:ResponseTimeThreshold", 2000.0), // 2 seconds default
            WindowMinutes = 5,
            Severity = AlertSeverity.Medium
        };

        // Authentication Failures Alert
        _thresholds["AuthenticationFailures"] = new AlertThreshold
        {
            Name = "Authentication Failures",
            Description = "High number of authentication failures detected",
            Threshold = _configuration.GetValue("Alerting:AuthFailureThreshold", 10.0), // 10 failures per minute
            WindowMinutes = 1,
            Severity = AlertSeverity.High
        };

        // System Resource Exhaustion Alert
        _thresholds["HighMemoryUsage"] = new AlertThreshold
        {
            Name = "High Memory Usage",
            Description = "Memory usage exceeds safe threshold",
            Threshold = _configuration.GetValue("Alerting:MemoryThreshold", 1000.0), // 1GB default
            WindowMinutes = 2,
            Severity = AlertSeverity.Critical
        };

        // Database Performance Alert
        _thresholds["DatabasePerformance"] = new AlertThreshold
        {
            Name = "Database Performance Issues",
            Description = "Database response time is too slow",
            Threshold = _configuration.GetValue("Alerting:DatabaseThreshold", 5000.0), // 5 seconds default
            WindowMinutes = 3,
            Severity = AlertSeverity.High
        };

        // SignalR Connection Issues Alert
        _thresholds["SignalRConnectionIssues"] = new AlertThreshold
        {
            Name = "SignalR Connection Issues",
            Description = "High number of SignalR connection failures",
            Threshold = _configuration.GetValue("Alerting:SignalRFailureThreshold", 5.0), // 5 failures per minute
            WindowMinutes = 2,
            Severity = AlertSeverity.Medium
        };
    }

    /// <summary>
    /// Reports a metric value for alert evaluation
    /// </summary>
    public void ReportMetric(string metricName, double value, Dictionary<string, string>? properties = null)
    {
        // Track the metric in Application Insights
        _telemetryClient.TrackMetric(metricName, value, properties);

        // Check if this metric should trigger an alert
        CheckMetricForAlert(metricName, value, properties);
    }

    /// <summary>
    /// Reports an error for alert evaluation
    /// </summary>
    public void ReportError(string errorType, string errorMessage, Dictionary<string, string>? properties = null)
    {
        // Track the error in Application Insights
        var errorEvent = new EventTelemetry($"Alert.Error.{errorType}");
        errorEvent.Properties["ErrorMessage"] = errorMessage;
        errorEvent.Properties["ErrorType"] = errorType;
        
        if (properties != null)
        {
            foreach (var prop in properties)
            {
                errorEvent.Properties[prop.Key] = prop.Value;
            }
        }

        _telemetryClient.TrackEvent(errorEvent);

        // Update error rate tracking
        UpdateErrorRateTracking(errorType);
    }

    /// <summary>
    /// Reports a performance metric for alert evaluation
    /// </summary>
    public void ReportPerformanceMetric(string operation, double durationMs, bool success, Dictionary<string, string>? properties = null)
    {
        var performanceEvent = new EventTelemetry($"Alert.Performance.{operation}");
        performanceEvent.Metrics["Duration"] = durationMs;
        performanceEvent.Properties["Success"] = success.ToString();
        
        if (properties != null)
        {
            foreach (var prop in properties)
            {
                performanceEvent.Properties[prop.Key] = prop.Value;
            }
        }

        _telemetryClient.TrackEvent(performanceEvent);

        // Check for performance degradation
        CheckPerformanceForAlert(operation, durationMs, success);
    }

    private void CheckMetricForAlert(string metricName, double value, Dictionary<string, string>? properties)
    {
        // Map metric names to alert types
        var alertType = metricName switch
        {
            var name when name.Contains("Memory") => "HighMemoryUsage",
            var name when name.Contains("Database") && name.Contains("Duration") => "DatabasePerformance",
            var name when name.Contains("SignalR") && name.Contains("Error") => "SignalRConnectionIssues",
            _ => null
        };

        if (alertType != null && _thresholds.TryGetValue(alertType, out var threshold))
        {
            if (value > threshold.Threshold)
            {
                TriggerAlert(alertType, threshold, value, properties);
            }
        }
    }

    private void UpdateErrorRateTracking(string errorType)
    {
        var key = $"ErrorRate_{errorType}";
        var now = DateTime.UtcNow;
        
        _alertStates.AddOrUpdate(key, new AlertState
        {
            LastOccurrence = now,
            Count = 1,
            WindowStart = now
        }, (_, state) =>
        {
            // Reset window if it's been more than the threshold window
            if (now - state.WindowStart > TimeSpan.FromMinutes(_thresholds["HighErrorRate"].WindowMinutes))
            {
                state.WindowStart = now;
                state.Count = 1;
            }
            else
            {
                state.Count++;
            }
            state.LastOccurrence = now;
            return state;
        });

        // Check if error rate exceeds threshold
        var alertState = _alertStates[key];
        var windowDuration = now - alertState.WindowStart;
        var errorRate = windowDuration.TotalMinutes > 0 ? alertState.Count / windowDuration.TotalMinutes : 0;

        if (errorRate > _thresholds["HighErrorRate"].Threshold)
        {
            TriggerAlert("HighErrorRate", _thresholds["HighErrorRate"], errorRate, new Dictionary<string, string>
            {
                ["ErrorType"] = errorType,
                ["ErrorCount"] = alertState.Count.ToString(),
                ["WindowMinutes"] = windowDuration.TotalMinutes.ToString("F2")
            });
        }
    }

    private void CheckPerformanceForAlert(string operation, double durationMs, bool success)
    {
        if (!success) return; // Only check performance for successful operations

        var key = $"Performance_{operation}";
        var now = DateTime.UtcNow;
        
        _alertStates.AddOrUpdate(key, new AlertState
        {
            LastOccurrence = now,
            Count = 1,
            TotalValue = durationMs,
            WindowStart = now
        }, (_, state) =>
        {
            // Reset window if it's been more than the threshold window
            if (now - state.WindowStart > TimeSpan.FromMinutes(_thresholds["PerformanceDegradation"].WindowMinutes))
            {
                state.WindowStart = now;
                state.Count = 1;
                state.TotalValue = durationMs;
            }
            else
            {
                state.Count++;
                state.TotalValue += durationMs;
            }
            state.LastOccurrence = now;
            return state;
        });

        // Check if average performance exceeds threshold
        var alertState = _alertStates[key];
        var averageDuration = alertState.Count > 0 ? alertState.TotalValue / alertState.Count : 0;

        if (averageDuration > _thresholds["PerformanceDegradation"].Threshold)
        {
            TriggerAlert("PerformanceDegradation", _thresholds["PerformanceDegradation"], averageDuration, new Dictionary<string, string>
            {
                ["Operation"] = operation,
                ["AverageDuration"] = averageDuration.ToString("F2"),
                ["SampleCount"] = alertState.Count.ToString()
            });
        }
    }

    private void TriggerAlert(string alertType, AlertThreshold threshold, double currentValue, Dictionary<string, string>? properties)
    {
        var alertKey = $"Alert_{alertType}";
        var now = DateTime.UtcNow;

        // Check if we've already triggered this alert recently (avoid spam)
        if (_alertStates.TryGetValue(alertKey, out var lastAlert) && 
            now - lastAlert.LastOccurrence < TimeSpan.FromMinutes(threshold.WindowMinutes))
        {
            return; // Don't trigger the same alert too frequently
        }

        // Update alert state
        _alertStates[alertKey] = new AlertState
        {
            LastOccurrence = now,
            Count = 1,
            TotalValue = currentValue
        };

        // Log the alert
        var logLevel = threshold.Severity switch
        {
            AlertSeverity.Critical => LogLevel.Critical,
            AlertSeverity.High => LogLevel.Error,
            AlertSeverity.Medium => LogLevel.Warning,
            _ => LogLevel.Information
        };

        _logger.Log(logLevel, "ALERT TRIGGERED: {AlertName} - {Description}. Current value: {CurrentValue}, Threshold: {Threshold}",
            threshold.Name, threshold.Description, currentValue, threshold.Threshold);

        // Send alert to Application Insights
        var alertEvent = new EventTelemetry("System.AlertTriggered");
        alertEvent.Properties["AlertType"] = alertType;
        alertEvent.Properties["AlertName"] = threshold.Name;
        alertEvent.Properties["Description"] = threshold.Description;
        alertEvent.Properties["Severity"] = threshold.Severity.ToString();
        alertEvent.Properties["CurrentValue"] = currentValue.ToString();
        alertEvent.Properties["Threshold"] = threshold.Threshold.ToString();
        alertEvent.Properties["TriggeredAt"] = now.ToString("O");

        if (properties != null)
        {
            foreach (var prop in properties)
            {
                alertEvent.Properties[$"Context_{prop.Key}"] = prop.Value;
            }
        }

        _telemetryClient.TrackEvent(alertEvent);

        // Track alert metrics
        _telemetryClient.TrackMetric($"Alerts.{alertType}.Triggered", 1);
        _telemetryClient.TrackMetric($"Alerts.Severity.{threshold.Severity}.Triggered", 1);

        // In a production environment, you would also:
        // 1. Send notifications via email, SMS, Slack, etc.
        // 2. Create tickets in your ticketing system
        // 3. Trigger automated remediation actions
        // 4. Update dashboards and status pages
    }

    private void CheckAlerts(object? state)
    {
        try
        {
            // This method runs periodically to check for alerts that might not be triggered by direct metrics
            // For example, checking if the application is still responsive, database connections are healthy, etc.
            
            _logger.LogDebug("Performing periodic alert checks");

            // Example: Check if we haven't received any requests recently (application might be down)
            // Example: Check system resources
            // Example: Check external service availability
            
            // This is where you would implement additional proactive monitoring
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during periodic alert check");
        }
    }

    public void Dispose()
    {
        _alertCheckTimer?.Dispose();
    }

    private class AlertThreshold
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double Threshold { get; set; }
        public int WindowMinutes { get; set; }
        public AlertSeverity Severity { get; set; }
    }

    private class AlertState
    {
        public DateTime LastOccurrence { get; set; }
        public DateTime WindowStart { get; set; }
        public int Count { get; set; }
        public double TotalValue { get; set; }
    }

    private enum AlertSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }
}
