const DashboardPage = () => {
  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <h1 className='text-2xl font-bold text-gray-900'>Dashboard</h1>
      </div>

      <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4'>
        {/* Stats Cards */}
        <div className='card'>
          <h3 className='text-sm font-medium text-gray-500'>Total Vehicles</h3>
          <p className='text-2xl font-bold text-gray-900'>24</p>
        </div>

        <div className='card'>
          <h3 className='text-sm font-medium text-gray-500'>Active Drivers</h3>
          <p className='text-2xl font-bold text-gray-900'>18</p>
        </div>

        <div className='card'>
          <h3 className='text-sm font-medium text-gray-500'>Active Alerts</h3>
          <p className='text-danger-600 text-2xl font-bold'>3</p>
        </div>

        <div className='card'>
          <h3 className='text-sm font-medium text-gray-500'>Fuel Efficiency</h3>
          <p className='text-success-600 text-2xl font-bold'>8.2 MPG</p>
        </div>
      </div>

      <div className='grid grid-cols-1 gap-6 lg:grid-cols-2'>
        <div className='card'>
          <h3 className='mb-4 text-lg font-medium text-gray-900'>
            Recent Activity
          </h3>
          <div className='space-y-3'>
            <div className='flex items-center justify-between'>
              <span className='text-sm text-gray-600'>
                Vehicle ABC-123 started route
              </span>
              <span className='text-xs text-gray-400'>2 min ago</span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-sm text-gray-600'>
                Driver John completed delivery
              </span>
              <span className='text-xs text-gray-400'>15 min ago</span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-sm text-gray-600'>
                Maintenance alert for XYZ-789
              </span>
              <span className='text-xs text-gray-400'>1 hour ago</span>
            </div>
          </div>
        </div>

        <div className='card'>
          <h3 className='mb-4 text-lg font-medium text-gray-900'>
            Fleet Status
          </h3>
          <div className='space-y-3'>
            <div className='flex items-center justify-between'>
              <span className='text-sm text-gray-600'>On Route</span>
              <span className='text-success-600 text-sm font-medium'>
                12 vehicles
              </span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-sm text-gray-600'>Idle</span>
              <span className='text-warning-600 text-sm font-medium'>
                8 vehicles
              </span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-sm text-gray-600'>Maintenance</span>
              <span className='text-danger-600 text-sm font-medium'>
                4 vehicles
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
