# FleetXQ Frontend Environment Variables

# API Configuration
VITE_API_BASE_URL=http://localhost:5000/api

# SignalR Hub URL for real-time communication
VITE_SIGNALR_HUB_URL=http://localhost:5000/hubs

# Application Configuration
VITE_APP_NAME=FleetXQ
VITE_APP_VERSION=1.0.0

# Feature Flags
VITE_ENABLE_TELEMETRY=true
VITE_ENABLE_REAL_TIME_UPDATES=true

# Map Configuration (if using maps)
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Development Configuration
VITE_DEBUG_MODE=false
