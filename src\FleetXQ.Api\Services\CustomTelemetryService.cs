using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace FleetXQ.Api.Services;

/// <summary>
/// Service for tracking custom business metrics and telemetry
/// </summary>
public class CustomTelemetryService
{
    private readonly TelemetryClient _telemetryClient;
    private readonly ILogger<CustomTelemetryService> _logger;
    private readonly ConcurrentDictionary<string, MetricCounter> _counters = new();
    private readonly ConcurrentDictionary<string, MetricGauge> _gauges = new();
    private readonly Timer _metricsTimer;

    public CustomTelemetryService(TelemetryClient telemetryClient, ILogger<CustomTelemetryService> logger)
    {
        _telemetryClient = telemetryClient;
        _logger = logger;
        
        // Initialize periodic metrics reporting
        _metricsTimer = new Timer(ReportPeriodicMetrics, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    #region API Endpoint Usage Statistics

    /// <summary>
    /// Tracks API endpoint usage
    /// </summary>
    public void TrackApiEndpointUsage(string endpoint, string method, int statusCode, long durationMs, string? userId = null)
    {
        var endpointKey = $"{method}_{endpoint}";
        
        // Track endpoint-specific metrics
        _telemetryClient.TrackMetric($"API.Endpoint.{endpointKey}.Requests", 1);
        _telemetryClient.TrackMetric($"API.Endpoint.{endpointKey}.Duration", durationMs);
        _telemetryClient.TrackMetric($"API.Endpoint.{endpointKey}.StatusCode.{statusCode}", 1);

        // Track overall API metrics
        _telemetryClient.TrackMetric("API.TotalRequests", 1);
        _telemetryClient.TrackMetric("API.AverageDuration", durationMs);

        // Track status code distribution
        var statusCategory = GetStatusCategory(statusCode);
        _telemetryClient.TrackMetric($"API.StatusCategory.{statusCategory}", 1);

        // Track user-specific metrics if available
        if (!string.IsNullOrEmpty(userId))
        {
            _telemetryClient.TrackMetric($"API.User.{userId}.Requests", 1);
        }

        // Track custom event for detailed analysis
        var endpointEvent = new EventTelemetry("API.EndpointUsage");
        endpointEvent.Properties["Endpoint"] = endpoint;
        endpointEvent.Properties["Method"] = method;
        endpointEvent.Properties["StatusCode"] = statusCode.ToString();
        endpointEvent.Properties["StatusCategory"] = statusCategory;
        endpointEvent.Properties["UserId"] = userId ?? "Anonymous";
        endpointEvent.Metrics["Duration"] = durationMs;
        endpointEvent.Timestamp = DateTime.UtcNow;

        _telemetryClient.TrackEvent(endpointEvent);

        // Update internal counters
        IncrementCounter($"api_endpoint_{endpointKey}_requests");
        UpdateGauge($"api_endpoint_{endpointKey}_avg_duration", durationMs);
    }

    /// <summary>
    /// Tracks API error rates
    /// </summary>
    public void TrackApiError(string endpoint, string method, int statusCode, string errorType, string? errorMessage = null, string? userId = null)
    {
        var endpointKey = $"{method}_{endpoint}";
        
        // Track error metrics
        _telemetryClient.TrackMetric($"API.Endpoint.{endpointKey}.Errors", 1);
        _telemetryClient.TrackMetric($"API.ErrorType.{errorType}", 1);
        _telemetryClient.TrackMetric("API.TotalErrors", 1);

        // Calculate error rate (this would typically be done with more sophisticated logic)
        var errorRate = CalculateErrorRate(endpointKey);
        _telemetryClient.TrackMetric($"API.Endpoint.{endpointKey}.ErrorRate", errorRate);

        // Track error event
        var errorEvent = new EventTelemetry("API.Error");
        errorEvent.Properties["Endpoint"] = endpoint;
        errorEvent.Properties["Method"] = method;
        errorEvent.Properties["StatusCode"] = statusCode.ToString();
        errorEvent.Properties["ErrorType"] = errorType;
        errorEvent.Properties["ErrorMessage"] = errorMessage ?? "Unknown";
        errorEvent.Properties["UserId"] = userId ?? "Anonymous";
        errorEvent.Timestamp = DateTime.UtcNow;

        _telemetryClient.TrackEvent(errorEvent);

        // Log error for immediate attention if critical
        if (statusCode >= 500)
        {
            _logger.LogError("API Error: {Method} {Endpoint} returned {StatusCode} - {ErrorType}: {ErrorMessage}",
                method, endpoint, statusCode, errorType, errorMessage);
        }
    }

    #endregion

    #region SignalR Connection Metrics

    /// <summary>
    /// Tracks real-time SignalR connection counts
    /// </summary>
    public void TrackSignalRConnections(string hubName, int connectionCount, int userCount)
    {
        _telemetryClient.TrackMetric($"SignalR.{hubName}.ActiveConnections", connectionCount);
        _telemetryClient.TrackMetric($"SignalR.{hubName}.ActiveUsers", userCount);
        _telemetryClient.TrackMetric("SignalR.TotalConnections", connectionCount);

        // Update internal gauges
        UpdateGauge($"signalr_{hubName}_connections", connectionCount);
        UpdateGauge($"signalr_{hubName}_users", userCount);

        // Track connection event
        var connectionEvent = new EventTelemetry("SignalR.ConnectionMetrics");
        connectionEvent.Properties["HubName"] = hubName;
        connectionEvent.Metrics["ConnectionCount"] = connectionCount;
        connectionEvent.Metrics["UserCount"] = userCount;
        connectionEvent.Timestamp = DateTime.UtcNow;

        _telemetryClient.TrackEvent(connectionEvent);
    }

    /// <summary>
    /// Tracks SignalR message throughput
    /// </summary>
    public void TrackSignalRMessageThroughput(string hubName, string methodName, int messageCount, long totalBytes)
    {
        _telemetryClient.TrackMetric($"SignalR.{hubName}.{methodName}.Messages", messageCount);
        _telemetryClient.TrackMetric($"SignalR.{hubName}.{methodName}.Bytes", totalBytes);
        _telemetryClient.TrackMetric($"SignalR.{hubName}.TotalMessages", messageCount);

        // Track message throughput event
        var throughputEvent = new EventTelemetry("SignalR.MessageThroughput");
        throughputEvent.Properties["HubName"] = hubName;
        throughputEvent.Properties["MethodName"] = methodName;
        throughputEvent.Metrics["MessageCount"] = messageCount;
        throughputEvent.Metrics["TotalBytes"] = totalBytes;
        throughputEvent.Metrics["AverageMessageSize"] = messageCount > 0 ? totalBytes / messageCount : 0;
        throughputEvent.Timestamp = DateTime.UtcNow;

        _telemetryClient.TrackEvent(throughputEvent);
    }

    #endregion

    #region Business-Specific Metrics

    /// <summary>
    /// Tracks vehicle-related metrics
    /// </summary>
    public void TrackVehicleMetrics(int totalVehicles, int activeVehicles, int onlineVehicles, int alertingVehicles)
    {
        _telemetryClient.TrackMetric("Business.Vehicles.Total", totalVehicles);
        _telemetryClient.TrackMetric("Business.Vehicles.Active", activeVehicles);
        _telemetryClient.TrackMetric("Business.Vehicles.Online", onlineVehicles);
        _telemetryClient.TrackMetric("Business.Vehicles.Alerting", alertingVehicles);

        // Calculate derived metrics
        var onlinePercentage = totalVehicles > 0 ? (double)onlineVehicles / totalVehicles * 100 : 0;
        var alertingPercentage = totalVehicles > 0 ? (double)alertingVehicles / totalVehicles * 100 : 0;

        _telemetryClient.TrackMetric("Business.Vehicles.OnlinePercentage", onlinePercentage);
        _telemetryClient.TrackMetric("Business.Vehicles.AlertingPercentage", alertingPercentage);

        // Update internal gauges
        UpdateGauge("business_vehicles_total", totalVehicles);
        UpdateGauge("business_vehicles_online", onlineVehicles);
        UpdateGauge("business_vehicles_alerting", alertingVehicles);

        // Track business metrics event
        var vehicleMetricsEvent = new EventTelemetry("Business.VehicleMetrics");
        vehicleMetricsEvent.Metrics["TotalVehicles"] = totalVehicles;
        vehicleMetricsEvent.Metrics["ActiveVehicles"] = activeVehicles;
        vehicleMetricsEvent.Metrics["OnlineVehicles"] = onlineVehicles;
        vehicleMetricsEvent.Metrics["AlertingVehicles"] = alertingVehicles;
        vehicleMetricsEvent.Metrics["OnlinePercentage"] = onlinePercentage;
        vehicleMetricsEvent.Metrics["AlertingPercentage"] = alertingPercentage;
        vehicleMetricsEvent.Timestamp = DateTime.UtcNow;

        _telemetryClient.TrackEvent(vehicleMetricsEvent);
    }

    /// <summary>
    /// Tracks alert-related metrics
    /// </summary>
    public void TrackAlertMetrics(string alertType, string severity, int count, string? vehicleId = null)
    {
        _telemetryClient.TrackMetric($"Business.Alerts.Type.{alertType}", count);
        _telemetryClient.TrackMetric($"Business.Alerts.Severity.{severity}", count);
        _telemetryClient.TrackMetric("Business.Alerts.Total", count);

        // Track per-vehicle alerts if specified
        if (!string.IsNullOrEmpty(vehicleId))
        {
            _telemetryClient.TrackMetric($"Business.Alerts.Vehicle.{vehicleId}", count);
        }

        // Update internal counters
        IncrementCounter($"business_alerts_{alertType}_{severity}", count);
        IncrementCounter("business_alerts_total", count);

        // Track alert event
        var alertEvent = new EventTelemetry("Business.AlertGenerated");
        alertEvent.Properties["AlertType"] = alertType;
        alertEvent.Properties["Severity"] = severity;
        alertEvent.Properties["VehicleId"] = vehicleId ?? "Unknown";
        alertEvent.Metrics["Count"] = count;
        alertEvent.Timestamp = DateTime.UtcNow;

        _telemetryClient.TrackEvent(alertEvent);
    }

    /// <summary>
    /// Tracks telemetry data processing metrics
    /// </summary>
    public void TrackTelemetryProcessing(int recordsProcessed, long processingTimeMs, int errorsCount, string? dataSource = null)
    {
        _telemetryClient.TrackMetric("Business.Telemetry.RecordsProcessed", recordsProcessed);
        _telemetryClient.TrackMetric("Business.Telemetry.ProcessingTime", processingTimeMs);
        _telemetryClient.TrackMetric("Business.Telemetry.Errors", errorsCount);

        // Calculate derived metrics
        var recordsPerSecond = processingTimeMs > 0 ? (double)recordsProcessed / (processingTimeMs / 1000.0) : 0;
        var errorRate = recordsProcessed > 0 ? (double)errorsCount / recordsProcessed * 100 : 0;

        _telemetryClient.TrackMetric("Business.Telemetry.RecordsPerSecond", recordsPerSecond);
        _telemetryClient.TrackMetric("Business.Telemetry.ErrorRate", errorRate);

        // Track by data source if specified
        if (!string.IsNullOrEmpty(dataSource))
        {
            _telemetryClient.TrackMetric($"Business.Telemetry.Source.{dataSource}.Records", recordsProcessed);
            _telemetryClient.TrackMetric($"Business.Telemetry.Source.{dataSource}.Errors", errorsCount);
        }

        // Track telemetry processing event
        var telemetryEvent = new EventTelemetry("Business.TelemetryProcessed");
        telemetryEvent.Properties["DataSource"] = dataSource ?? "Unknown";
        telemetryEvent.Metrics["RecordsProcessed"] = recordsProcessed;
        telemetryEvent.Metrics["ProcessingTimeMs"] = processingTimeMs;
        telemetryEvent.Metrics["ErrorsCount"] = errorsCount;
        telemetryEvent.Metrics["RecordsPerSecond"] = recordsPerSecond;
        telemetryEvent.Metrics["ErrorRate"] = errorRate;
        telemetryEvent.Timestamp = DateTime.UtcNow;

        _telemetryClient.TrackEvent(telemetryEvent);
    }

    #endregion

    #region System Health Metrics

    /// <summary>
    /// Tracks system health metrics
    /// </summary>
    public void TrackSystemHealth()
    {
        var process = Process.GetCurrentProcess();
        
        // Memory metrics
        var workingSet = process.WorkingSet64;
        var privateMemory = process.PrivateMemorySize64;
        var gcMemory = GC.GetTotalMemory(false);

        _telemetryClient.TrackMetric("System.Memory.WorkingSet", workingSet);
        _telemetryClient.TrackMetric("System.Memory.Private", privateMemory);
        _telemetryClient.TrackMetric("System.Memory.GC", gcMemory);

        // CPU and thread metrics
        var cpuTime = process.TotalProcessorTime.TotalMilliseconds;
        var threadCount = process.Threads.Count;

        _telemetryClient.TrackMetric("System.CPU.TotalTime", cpuTime);
        _telemetryClient.TrackMetric("System.Threads.Count", threadCount);

        // GC metrics
        var gen0Collections = GC.CollectionCount(0);
        var gen1Collections = GC.CollectionCount(1);
        var gen2Collections = GC.CollectionCount(2);

        _telemetryClient.TrackMetric("System.GC.Gen0Collections", gen0Collections);
        _telemetryClient.TrackMetric("System.GC.Gen1Collections", gen1Collections);
        _telemetryClient.TrackMetric("System.GC.Gen2Collections", gen2Collections);

        // Update internal gauges
        UpdateGauge("system_memory_working_set", workingSet);
        UpdateGauge("system_threads_count", threadCount);
        UpdateGauge("system_gc_memory", gcMemory);
    }

    #endregion

    #region Helper Methods

    private string GetStatusCategory(int statusCode)
    {
        return statusCode switch
        {
            >= 200 and < 300 => "Success",
            >= 300 and < 400 => "Redirect",
            >= 400 and < 500 => "ClientError",
            >= 500 => "ServerError",
            _ => "Unknown"
        };
    }

    private double CalculateErrorRate(string endpointKey)
    {
        // This is a simplified calculation - in a real implementation,
        // you would maintain sliding windows of success/error counts
        var errorCounter = _counters.GetOrAdd($"api_endpoint_{endpointKey}_errors", _ => new MetricCounter());
        var requestCounter = _counters.GetOrAdd($"api_endpoint_{endpointKey}_requests", _ => new MetricCounter());
        
        var totalRequests = requestCounter.Value;
        var totalErrors = errorCounter.Value;
        
        return totalRequests > 0 ? (double)totalErrors / totalRequests * 100 : 0;
    }

    private void IncrementCounter(string key, long increment = 1)
    {
        _counters.AddOrUpdate(key, new MetricCounter { Value = increment }, (_, counter) =>
        {
            counter.Value += increment;
            return counter;
        });
    }

    private void UpdateGauge(string key, double value)
    {
        _gauges.AddOrUpdate(key, new MetricGauge { Value = value, LastUpdated = DateTime.UtcNow }, (_, gauge) =>
        {
            gauge.Value = value;
            gauge.LastUpdated = DateTime.UtcNow;
            return gauge;
        });
    }

    private void ReportPeriodicMetrics(object? state)
    {
        try
        {
            // Report system health metrics
            TrackSystemHealth();

            // Report counter values
            foreach (var counter in _counters)
            {
                _telemetryClient.TrackMetric($"Periodic.Counter.{counter.Key}", counter.Value.Value);
            }

            // Report gauge values
            foreach (var gauge in _gauges)
            {
                _telemetryClient.TrackMetric($"Periodic.Gauge.{gauge.Key}", gauge.Value.Value);
            }

            _logger.LogDebug("Periodic metrics reported successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reporting periodic metrics");
        }
    }

    public void Dispose()
    {
        _metricsTimer?.Dispose();
    }

    #endregion

    private class MetricCounter
    {
        public long Value { get; set; }
    }

    private class MetricGauge
    {
        public double Value { get; set; }
        public DateTime LastUpdated { get; set; }
    }
}
