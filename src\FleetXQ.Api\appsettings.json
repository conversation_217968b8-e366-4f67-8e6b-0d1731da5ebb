{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Jwt": {"Key": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "FleetXQ", "Audience": "FleetXQ-Users", "ExpiryInMinutes": 60, "RefreshTokenExpiryInDays": 7}, "PasswordPolicy": {"MinLength": 8, "MaxLength": 128, "RequireUppercase": true, "RequireLowercase": true, "RequireDigit": true, "RequireSpecialChar": true, "MinUniqueChars": 4}, "RateLimit": {"Login": {"MaxRequests": 5, "TimeWindowMinutes": 15, "LockoutDurationMinutes": 30}, "Refresh": {"MaxRequests": 10, "TimeWindowMinutes": 5, "LockoutDurationMinutes": 15}, "PasswordReset": {"MaxRequests": 3, "TimeWindowHours": 1, "LockoutDurationHours": 2}}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "https://localhost:3000", "http://localhost:5173", "https://localhost:5173"]}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.ApplicationInsights", "Serilog.Enrichers.Environment", "Serilog.Enrichers.Process", "Serilog.Enrichers.AspNetCore"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "System": "Warning", "System.Net.Http.HttpClient": "Warning", "Microsoft.AspNetCore.SignalR": "Information", "FleetXQ": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {CorrelationId} {SourceContext} {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/fleetxq-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 104857600, "rollOnFileSizeLimit": true, "shared": true, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {CorrelationId} {SourceContext} {UserId} {UserName} {RequestPath} {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/fleetxq-errors-.log", "rollingInterval": "Day", "retainedFileCountLimit": 90, "restrictedToMinimumLevel": "Error", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {CorrelationId} {SourceContext} {UserId} {UserName} {RequestPath} {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "ApplicationInsights", "Args": {"restrictedToMinimumLevel": "Information", "telemetryConverter": "Serilog.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId", "WithProcessId", "WithProcessName", "WithEnvironmentName", "WithEnvironmentUserName", "WithCorrelationId", "WithClientIp", "WithRequestId", "WithRequestPath", "WithUserClaims"], "Properties": {"Application": "FleetXQ.Api", "Environment": "Production"}}, "ApplicationInsights": {"ConnectionString": "", "EnableAdaptiveSampling": true, "EnablePerformanceCounterCollectionModule": true, "EnableRequestTrackingTelemetryModule": true, "EnableDependencyTrackingTelemetryModule": true, "EnableEventCounterCollectionModule": true, "EnableHeartbeat": true}, "Alerting": {"ErrorRateThreshold": 5.0, "ResponseTimeThreshold": 2000.0, "AuthFailureThreshold": 10.0, "MemoryThreshold": 1000.0, "DatabaseThreshold": 5000.0, "SignalRFailureThreshold": 5.0}}