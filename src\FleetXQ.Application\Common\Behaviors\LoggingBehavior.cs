using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.AspNetCore.Http;
using Serilog.Context;
using System.Diagnostics;
using System.Text.Json;
using System.Security.Claims;
using System.Reflection;

namespace FleetXQ.Application.Common.Behaviors;

/// <summary>
/// Pipeline behavior for request/response logging
/// </summary>
/// <typeparam name="TRequest">The request type</typeparam>
/// <typeparam name="TResponse">The response type</typeparam>
public sealed class LoggingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    private readonly ILogger<LoggingBehavior<TRequest, TResponse>> _logger;
    private readonly TelemetryClient _telemetryClient;
    private readonly IHttpContextAccessor _httpContextAccessor;

    private static readonly string[] SensitiveProperties = { "password", "token", "secret", "key", "connectionstring", "hash", "pin", "ssn", "creditcard" };
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false,
        DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
    };

    /// <summary>
    /// Initializes a new instance of the <see cref="LoggingBehavior{TRequest, TResponse}"/> class
    /// </summary>
    /// <param name="logger">The logger</param>
    /// <param name="telemetryClient">The Application Insights telemetry client</param>
    /// <param name="httpContextAccessor">The HTTP context accessor</param>
    public LoggingBehavior(
        ILogger<LoggingBehavior<TRequest, TResponse>> logger,
        TelemetryClient telemetryClient,
        IHttpContextAccessor httpContextAccessor)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _telemetryClient = telemetryClient ?? throw new ArgumentNullException(nameof(telemetryClient));
        _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
    }

    /// <summary>
    /// Handles the request logging
    /// </summary>
    /// <param name="request">The request</param>
    /// <param name="next">The next handler in the pipeline</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The response</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var requestId = Guid.NewGuid();
        var correlationId = GetCorrelationId();
        var stopwatch = Stopwatch.StartNew();
        var requestStartTime = DateTime.UtcNow;

        // Get user context
        var userContext = GetUserContext();

        // Add structured logging context
        using (LogContext.PushProperty("RequestId", requestId))
        using (LogContext.PushProperty("CorrelationId", correlationId))
        using (LogContext.PushProperty("RequestName", requestName))
        using (LogContext.PushProperty("UserId", userContext.UserId))
        using (LogContext.PushProperty("UserName", userContext.UserName))
        {
            _logger.LogInformation("Starting CQRS request {RequestName} with ID {RequestId} for user {UserName} ({UserId})",
                requestName, requestId, userContext.UserName ?? "Anonymous", userContext.UserId ?? "N/A");

            // Log request details (be careful with sensitive data)
            if (_logger.IsEnabled(LogLevel.Debug))
            {
                var requestJson = SerializeRequest(request);
                _logger.LogDebug("Request {RequestName} ({RequestId}) details: {RequestData}",
                    requestName, requestId, requestJson);
            }

            // Track custom event in Application Insights
            var eventTelemetry = new EventTelemetry($"CQRS.{requestName}.Started");
            eventTelemetry.Properties["RequestId"] = requestId.ToString();
            eventTelemetry.Properties["CorrelationId"] = correlationId;
            eventTelemetry.Properties["UserId"] = userContext.UserId ?? "Anonymous";
            eventTelemetry.Properties["UserName"] = userContext.UserName ?? "Anonymous";
            eventTelemetry.Properties["RequestType"] = GetRequestType(requestName);
            eventTelemetry.Timestamp = requestStartTime;
            _telemetryClient.TrackEvent(eventTelemetry);

            try
            {
                var response = await next();

                stopwatch.Stop();

                _logger.LogInformation("Completed CQRS request {RequestName} with ID {RequestId} in {ElapsedMilliseconds}ms",
                    requestName, requestId, stopwatch.ElapsedMilliseconds);

                // Log response details for debug (be careful with sensitive data)
                if (_logger.IsEnabled(LogLevel.Debug))
                {
                    var responseJson = SerializeResponse(response);
                    _logger.LogDebug("Response for {RequestName} ({RequestId}): {ResponseData}",
                        requestName, requestId, responseJson);
                }

                // Track successful completion in Application Insights
                var completedEvent = new EventTelemetry($"CQRS.{requestName}.Completed");
                completedEvent.Properties["RequestId"] = requestId.ToString();
                completedEvent.Properties["CorrelationId"] = correlationId;
                completedEvent.Properties["UserId"] = userContext.UserId ?? "Anonymous";
                completedEvent.Properties["ElapsedMs"] = stopwatch.ElapsedMilliseconds.ToString();
                completedEvent.Properties["Success"] = "true";
                completedEvent.Metrics["Duration"] = stopwatch.ElapsedMilliseconds;
                _telemetryClient.TrackEvent(completedEvent);

                // Track performance metrics
                _telemetryClient.TrackMetric($"CQRS.{requestName}.Duration", stopwatch.ElapsedMilliseconds);
                _telemetryClient.TrackMetric($"CQRS.{requestName}.Success", 1);

                // Log slow requests
                if (stopwatch.ElapsedMilliseconds > 2000)
                {
                    _logger.LogWarning("Slow CQRS request detected: {RequestName} took {ElapsedMilliseconds}ms",
                        requestName, stopwatch.ElapsedMilliseconds);
                    _telemetryClient.TrackMetric($"CQRS.{requestName}.Slow", 1);
                }

                return response;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                _logger.LogError(ex, "CQRS request {RequestName} with ID {RequestId} failed after {ElapsedMilliseconds}ms: {ErrorMessage}",
                    requestName, requestId, stopwatch.ElapsedMilliseconds, ex.Message);

                // Track failure in Application Insights
                var failedEvent = new EventTelemetry($"CQRS.{requestName}.Failed");
                failedEvent.Properties["RequestId"] = requestId.ToString();
                failedEvent.Properties["CorrelationId"] = correlationId;
                failedEvent.Properties["UserId"] = userContext.UserId ?? "Anonymous";
                failedEvent.Properties["ElapsedMs"] = stopwatch.ElapsedMilliseconds.ToString();
                failedEvent.Properties["Success"] = "false";
                failedEvent.Properties["ExceptionType"] = ex.GetType().Name;
                failedEvent.Properties["ExceptionMessage"] = ex.Message;
                failedEvent.Metrics["Duration"] = stopwatch.ElapsedMilliseconds;
                _telemetryClient.TrackEvent(failedEvent);

                // Track exception
                _telemetryClient.TrackException(ex, new Dictionary<string, string>
                {
                    ["RequestName"] = requestName,
                    ["RequestId"] = requestId.ToString(),
                    ["CorrelationId"] = correlationId,
                    ["UserId"] = userContext.UserId ?? "Anonymous"
                });

                // Track failure metrics
                _telemetryClient.TrackMetric($"CQRS.{requestName}.Failure", 1);
                _telemetryClient.TrackMetric($"CQRS.{requestName}.Duration", stopwatch.ElapsedMilliseconds);

                throw;
            }
        }
    }
    }

    /// <summary>
    /// Gets the correlation ID from the current HTTP context
    /// </summary>
    /// <returns>The correlation ID</returns>
    private string GetCorrelationId()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext?.Request.Headers.TryGetValue("X-Correlation-ID", out var correlationId) == true)
        {
            return correlationId.FirstOrDefault() ?? Guid.NewGuid().ToString();
        }
        return Guid.NewGuid().ToString();
    }

    /// <summary>
    /// Gets the user context from the current HTTP context
    /// </summary>
    /// <returns>The user context</returns>
    private UserContext GetUserContext()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        var user = httpContext?.User;

        return new UserContext
        {
            UserId = user?.FindFirst(ClaimTypes.NameIdentifier)?.Value,
            UserName = user?.FindFirst(ClaimTypes.Name)?.Value ?? user?.FindFirst("name")?.Value,
            UserEmail = user?.FindFirst(ClaimTypes.Email)?.Value,
            IsAuthenticated = user?.Identity?.IsAuthenticated ?? false,
            Roles = user?.FindAll(ClaimTypes.Role)?.Select(c => c.Value).ToArray() ?? Array.Empty<string>()
        };
    }

    /// <summary>
    /// Determines the request type (Command or Query)
    /// </summary>
    /// <param name="requestName">The request name</param>
    /// <returns>The request type</returns>
    private static string GetRequestType(string requestName)
    {
        return requestName.EndsWith("Command", StringComparison.OrdinalIgnoreCase) ? "Command" : "Query";
    }

    /// <summary>
    /// Serializes the request for logging (with sensitive data filtering)
    /// </summary>
    /// <param name="request">The request to serialize</param>
    /// <returns>The serialized request</returns>
    private static string SerializeRequest(TRequest request)
    {
        try
        {
            // Create a copy of the request with sensitive data masked
            var requestCopy = MaskSensitiveData(request);
            return JsonSerializer.Serialize(requestCopy, JsonOptions);
        }
        catch (Exception)
        {
            return $"[Unable to serialize {typeof(TRequest).Name}]";
        }
    }

    /// <summary>
    /// Serializes the response for logging (with sensitive data filtering)
    /// </summary>
    /// <param name="response">The response to serialize</param>
    /// <returns>The serialized response</returns>
    private static string SerializeResponse(TResponse response)
    {
        try
        {
            // Create a copy of the response with sensitive data masked
            var responseCopy = MaskSensitiveData(response);
            return JsonSerializer.Serialize(responseCopy, JsonOptions);
        }
        catch (Exception)
        {
            return $"[Unable to serialize {typeof(TResponse).Name}]";
        }
    }

    /// <summary>
    /// Masks sensitive data in objects for logging
    /// </summary>
    /// <param name="obj">The object to mask</param>
    /// <returns>The object with sensitive data masked</returns>
    private static object MaskSensitiveData(object obj)
    {
        if (obj == null) return obj;

        var type = obj.GetType();
        var properties = type.GetProperties();
        var maskedObj = Activator.CreateInstance(type);

        if (maskedObj == null) return obj;

        foreach (var property in properties)
        {
            if (!property.CanRead || !property.CanWrite) continue;

            var value = property.GetValue(obj);
            
            // Mask sensitive properties
            if (IsSensitiveProperty(property.Name))
            {
                property.SetValue(maskedObj, "[MASKED]");
            }
            else
            {
                property.SetValue(maskedObj, value);
            }
        }

        return maskedObj;
    }

    /// <summary>
    /// Determines if a property contains sensitive data
    /// </summary>
    /// <param name="propertyName">The property name</param>
    /// <returns>True if the property is sensitive</returns>
    private static bool IsSensitiveProperty(string propertyName)
    {
        return SensitiveProperties.Any(sensitive =>
            propertyName.Contains(sensitive, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// User context information
    /// </summary>
    private record UserContext
    {
        public string? UserId { get; init; }
        public string? UserName { get; init; }
        public string? UserEmail { get; init; }
        public bool IsAuthenticated { get; init; }
        public string[] Roles { get; init; } = Array.Empty<string>();
    }
}
