using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Serilog;
using Serilog.Context;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text.Json;

namespace FleetXQ.Api.Middleware;

/// <summary>
/// Middleware for comprehensive security event logging
/// </summary>
public class SecurityEventLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<SecurityEventLoggingMiddleware> _logger;
    private readonly TelemetryClient _telemetryClient;
    private readonly IConfiguration _configuration;

    public SecurityEventLoggingMiddleware(
        RequestDelegate next,
        ILogger<SecurityEventLoggingMiddleware> logger,
        TelemetryClient telemetryClient,
        IConfiguration configuration)
    {
        _next = next;
        _logger = logger;
        _telemetryClient = telemetryClient;
        _configuration = configuration;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Log authentication events
        await LogAuthenticationEvents(context);

        // Store original response status for authorization logging
        var originalStatusCode = context.Response.StatusCode;

        await _next(context);

        // Log authorization events after request processing
        await LogAuthorizationEvents(context, originalStatusCode);
    }

    private async Task LogAuthenticationEvents(HttpContext context)
    {
        var request = context.Request;
        var clientIp = GetClientIpAddress(context);
        var userAgent = request.Headers.UserAgent.ToString();
        var correlationId = GetCorrelationId(context);

        // Check for authentication header
        if (request.Headers.ContainsKey("Authorization"))
        {
            var authHeader = request.Headers.Authorization.FirstOrDefault();
            
            if (!string.IsNullOrEmpty(authHeader))
            {
                await LogJwtTokenValidation(context, authHeader, clientIp, userAgent, correlationId);
            }
        }

        // Log login attempts (for auth endpoints)
        if (IsAuthenticationEndpoint(request.Path))
        {
            await LogLoginAttempt(context, clientIp, userAgent, correlationId);
        }
    }

    private async Task LogJwtTokenValidation(HttpContext context, string authHeader, string clientIp, string userAgent, string correlationId)
    {
        try
        {
            if (authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                var token = authHeader.Substring("Bearer ".Length).Trim();
                var handler = new JwtSecurityTokenHandler();

                if (handler.CanReadToken(token))
                {
                    var jwtToken = handler.ReadJwtToken(token);
                    var userId = jwtToken.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
                    var userName = jwtToken.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name)?.Value;
                    var email = jwtToken.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)?.Value;
                    var roles = jwtToken.Claims.Where(c => c.Type == ClaimTypes.Role).Select(c => c.Value).ToArray();

                    using (LogContext.PushProperty("CorrelationId", correlationId))
                    using (LogContext.PushProperty("UserId", userId))
                    using (LogContext.PushProperty("UserName", userName))
                    using (LogContext.PushProperty("ClientIp", clientIp))
                    using (LogContext.PushProperty("SecurityEvent", "JWT_TOKEN_VALIDATION"))
                    {
                        var tokenInfo = new
                        {
                            UserId = userId,
                            UserName = userName,
                            Email = email,
                            Roles = roles,
                            Issuer = jwtToken.Issuer,
                            Audience = jwtToken.Audiences.FirstOrDefault(),
                            IssuedAt = jwtToken.IssuedAt,
                            ValidFrom = jwtToken.ValidFrom,
                            ValidTo = jwtToken.ValidTo,
                            IsExpired = jwtToken.ValidTo < DateTime.UtcNow,
                            ClientIp = clientIp,
                            UserAgent = userAgent,
                            RequestPath = context.Request.Path.Value,
                            Timestamp = DateTime.UtcNow
                        };

                        if (jwtToken.ValidTo < DateTime.UtcNow)
                        {
                            _logger.LogWarning("Expired JWT token used by {UserName} ({UserId}) from {ClientIp}", 
                                userName ?? "Unknown", userId ?? "Unknown", clientIp);

                            // Track expired token usage
                            var expiredTokenEvent = new EventTelemetry("Security.ExpiredTokenUsed");
                            expiredTokenEvent.Properties["UserId"] = userId ?? "Unknown";
                            expiredTokenEvent.Properties["UserName"] = userName ?? "Unknown";
                            expiredTokenEvent.Properties["ClientIp"] = clientIp;
                            expiredTokenEvent.Properties["CorrelationId"] = correlationId;
                            _telemetryClient.TrackEvent(expiredTokenEvent);
                        }
                        else
                        {
                            _logger.LogDebug("Valid JWT token for {UserName} ({UserId}) from {ClientIp}", 
                                userName ?? "Unknown", userId ?? "Unknown", clientIp);
                        }

                        // Track token validation metrics
                        _telemetryClient.TrackMetric("Security.TokenValidation", 1);
                        
                        // Detect suspicious activity
                        await DetectSuspiciousActivity(context, tokenInfo, correlationId);
                    }
                }
                else
                {
                    _logger.LogWarning("Invalid JWT token format from {ClientIp} for path {RequestPath}", 
                        clientIp, context.Request.Path);

                    var invalidTokenEvent = new EventTelemetry("Security.InvalidTokenFormat");
                    invalidTokenEvent.Properties["ClientIp"] = clientIp;
                    invalidTokenEvent.Properties["RequestPath"] = context.Request.Path.Value;
                    invalidTokenEvent.Properties["CorrelationId"] = correlationId;
                    _telemetryClient.TrackEvent(invalidTokenEvent);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating JWT token from {ClientIp}", clientIp);
            
            var tokenErrorEvent = new EventTelemetry("Security.TokenValidationError");
            tokenErrorEvent.Properties["ClientIp"] = clientIp;
            tokenErrorEvent.Properties["ErrorMessage"] = ex.Message;
            tokenErrorEvent.Properties["CorrelationId"] = correlationId;
            _telemetryClient.TrackEvent(tokenErrorEvent);
        }
    }

    private async Task LogLoginAttempt(HttpContext context, string clientIp, string userAgent, string correlationId)
    {
        if (context.Request.Method == "POST")
        {
            try
            {
                context.Request.EnableBuffering();
                context.Request.Body.Position = 0;
                
                using var reader = new StreamReader(context.Request.Body, leaveOpen: true);
                var body = await reader.ReadToEndAsync();
                context.Request.Body.Position = 0;

                if (!string.IsNullOrEmpty(body))
                {
                    var loginData = JsonSerializer.Deserialize<JsonElement>(body);
                    var username = loginData.TryGetProperty("username", out var usernameElement) ? 
                        usernameElement.GetString() : 
                        loginData.TryGetProperty("email", out var emailElement) ? emailElement.GetString() : "Unknown";

                    using (LogContext.PushProperty("CorrelationId", correlationId))
                    using (LogContext.PushProperty("UserName", username))
                    using (LogContext.PushProperty("ClientIp", clientIp))
                    using (LogContext.PushProperty("SecurityEvent", "LOGIN_ATTEMPT"))
                    {
                        _logger.LogInformation("Login attempt for user {UserName} from {ClientIp}", 
                            username, clientIp);

                        var loginAttemptEvent = new EventTelemetry("Security.LoginAttempt");
                        loginAttemptEvent.Properties["UserName"] = username ?? "Unknown";
                        loginAttemptEvent.Properties["ClientIp"] = clientIp;
                        loginAttemptEvent.Properties["UserAgent"] = userAgent;
                        loginAttemptEvent.Properties["CorrelationId"] = correlationId;
                        _telemetryClient.TrackEvent(loginAttemptEvent);

                        // Track login attempt metrics
                        _telemetryClient.TrackMetric("Security.LoginAttempts", 1);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to log login attempt from {ClientIp}", clientIp);
            }
        }
    }

    private async Task LogAuthorizationEvents(HttpContext context, int originalStatusCode)
    {
        var user = context.User;
        var clientIp = GetClientIpAddress(context);
        var correlationId = GetCorrelationId(context);
        var statusCode = context.Response.StatusCode;

        // Log authorization failures
        if (statusCode == 401)
        {
            using (LogContext.PushProperty("CorrelationId", correlationId))
            using (LogContext.PushProperty("ClientIp", clientIp))
            using (LogContext.PushProperty("SecurityEvent", "AUTHENTICATION_FAILURE"))
            {
                _logger.LogWarning("Authentication failure for {RequestPath} from {ClientIp}", 
                    context.Request.Path, clientIp);

                var authFailureEvent = new EventTelemetry("Security.AuthenticationFailure");
                authFailureEvent.Properties["RequestPath"] = context.Request.Path.Value;
                authFailureEvent.Properties["ClientIp"] = clientIp;
                authFailureEvent.Properties["Method"] = context.Request.Method;
                authFailureEvent.Properties["CorrelationId"] = correlationId;
                _telemetryClient.TrackEvent(authFailureEvent);

                _telemetryClient.TrackMetric("Security.AuthenticationFailures", 1);
            }
        }
        else if (statusCode == 403)
        {
            var userId = user?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userName = user?.FindFirst(ClaimTypes.Name)?.Value;

            using (LogContext.PushProperty("CorrelationId", correlationId))
            using (LogContext.PushProperty("UserId", userId))
            using (LogContext.PushProperty("UserName", userName))
            using (LogContext.PushProperty("ClientIp", clientIp))
            using (LogContext.PushProperty("SecurityEvent", "AUTHORIZATION_FAILURE"))
            {
                _logger.LogWarning("Authorization failure for user {UserName} ({UserId}) accessing {RequestPath} from {ClientIp}", 
                    userName ?? "Unknown", userId ?? "Unknown", context.Request.Path, clientIp);

                var authzFailureEvent = new EventTelemetry("Security.AuthorizationFailure");
                authzFailureEvent.Properties["UserId"] = userId ?? "Unknown";
                authzFailureEvent.Properties["UserName"] = userName ?? "Unknown";
                authzFailureEvent.Properties["RequestPath"] = context.Request.Path.Value;
                authzFailureEvent.Properties["ClientIp"] = clientIp;
                authzFailureEvent.Properties["Method"] = context.Request.Method;
                authzFailureEvent.Properties["CorrelationId"] = correlationId;
                _telemetryClient.TrackEvent(authzFailureEvent);

                _telemetryClient.TrackMetric("Security.AuthorizationFailures", 1);
            }
        }
        else if (statusCode == 200 && user?.Identity?.IsAuthenticated == true)
        {
            // Log successful authenticated access
            var userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userName = user.FindFirst(ClaimTypes.Name)?.Value;

            using (LogContext.PushProperty("CorrelationId", correlationId))
            using (LogContext.PushProperty("UserId", userId))
            using (LogContext.PushProperty("UserName", userName))
            using (LogContext.PushProperty("SecurityEvent", "AUTHENTICATED_ACCESS"))
            {
                _logger.LogDebug("Successful authenticated access by {UserName} ({UserId}) to {RequestPath} from {ClientIp}", 
                    userName ?? "Unknown", userId ?? "Unknown", context.Request.Path, clientIp);

                _telemetryClient.TrackMetric("Security.AuthenticatedAccess", 1);
            }
        }

        // Log successful login (for auth endpoints with 200 status)
        if (IsAuthenticationEndpoint(context.Request.Path) && statusCode == 200)
        {
            await LogSuccessfulLogin(context, clientIp, correlationId);
        }
    }

    private async Task LogSuccessfulLogin(HttpContext context, string clientIp, string correlationId)
    {
        var user = context.User;
        var userId = user?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var userName = user?.FindFirst(ClaimTypes.Name)?.Value;

        using (LogContext.PushProperty("CorrelationId", correlationId))
        using (LogContext.PushProperty("UserId", userId))
        using (LogContext.PushProperty("UserName", userName))
        using (LogContext.PushProperty("ClientIp", clientIp))
        using (LogContext.PushProperty("SecurityEvent", "LOGIN_SUCCESS"))
        {
            _logger.LogInformation("Successful login for user {UserName} ({UserId}) from {ClientIp}", 
                userName ?? "Unknown", userId ?? "Unknown", clientIp);

            var loginSuccessEvent = new EventTelemetry("Security.LoginSuccess");
            loginSuccessEvent.Properties["UserId"] = userId ?? "Unknown";
            loginSuccessEvent.Properties["UserName"] = userName ?? "Unknown";
            loginSuccessEvent.Properties["ClientIp"] = clientIp;
            loginSuccessEvent.Properties["CorrelationId"] = correlationId;
            _telemetryClient.TrackEvent(loginSuccessEvent);

            _telemetryClient.TrackMetric("Security.LoginSuccess", 1);
        }
    }

    private async Task DetectSuspiciousActivity(HttpContext context, object tokenInfo, string correlationId)
    {
        // Implement suspicious activity detection logic
        var clientIp = GetClientIpAddress(context);
        var userAgent = context.Request.Headers.UserAgent.ToString();
        
        // Example: Detect multiple rapid requests from same IP
        // Example: Detect unusual user agent patterns
        // Example: Detect access from unusual locations
        // This would typically involve caching and rate limiting logic
        
        // For now, just log debug information
        _logger.LogDebug("Security check completed for request from {ClientIp}", clientIp);
    }

    private string GetClientIpAddress(HttpContext context)
    {
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }

    private string GetCorrelationId(HttpContext context)
    {
        if (context.Request.Headers.TryGetValue("X-Correlation-ID", out var correlationId))
        {
            return correlationId.FirstOrDefault() ?? Guid.NewGuid().ToString();
        }
        return Guid.NewGuid().ToString();
    }

    private bool IsAuthenticationEndpoint(PathString path)
    {
        var authPaths = new[] { "/api/auth/login", "/api/auth/register", "/api/auth/refresh" };
        return authPaths.Any(authPath => path.StartsWithSegments(authPath, StringComparison.OrdinalIgnoreCase));
    }
}
