using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.AspNetCore.SignalR;
using Serilog;
using Serilog.Context;
using System.Collections.Concurrent;
using System.Security.Claims;
using System.Diagnostics;

namespace FleetXQ.Api.Services;

/// <summary>
/// Service for comprehensive SignalR connection and message logging
/// </summary>
public class SignalRLoggingService
{
    private readonly ILogger<SignalRLoggingService> _logger;
    private readonly TelemetryClient _telemetryClient;
    private readonly ConcurrentDictionary<string, ConnectionInfo> _connections = new();
    private readonly ConcurrentDictionary<string, List<MessageInfo>> _recentMessages = new();

    public SignalRLoggingService(
        ILogger<SignalRLoggingService> logger,
        TelemetryClient telemetryClient)
    {
        _logger = logger;
        _telemetryClient = telemetryClient;
    }

    /// <summary>
    /// Logs SignalR connection establishment
    /// </summary>
    public async Task LogConnectionAsync(HubCallerContext context, string hubName)
    {
        var connectionId = context.ConnectionId;
        var userId = context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var userName = context.User?.FindFirst(ClaimTypes.Name)?.Value;
        var userAgent = context.GetHttpContext()?.Request.Headers.UserAgent.ToString();
        var clientIp = GetClientIpAddress(context.GetHttpContext());
        var correlationId = GetCorrelationId(context.GetHttpContext());

        var connectionInfo = new ConnectionInfo
        {
            ConnectionId = connectionId,
            UserId = userId,
            UserName = userName,
            HubName = hubName,
            ClientIp = clientIp,
            UserAgent = userAgent,
            ConnectedAt = DateTime.UtcNow,
            CorrelationId = correlationId
        };

        _connections.TryAdd(connectionId, connectionInfo);

        using (LogContext.PushProperty("CorrelationId", correlationId))
        using (LogContext.PushProperty("ConnectionId", connectionId))
        using (LogContext.PushProperty("UserId", userId))
        using (LogContext.PushProperty("UserName", userName))
        using (LogContext.PushProperty("HubName", hubName))
        using (LogContext.PushProperty("ClientIp", clientIp))
        using (LogContext.PushProperty("SignalREvent", "CONNECTION_ESTABLISHED"))
        {
            _logger.LogInformation("SignalR connection established: User {UserName} ({UserId}) connected to {HubName} from {ClientIp}",
                userName ?? "Anonymous", userId ?? "Unknown", hubName, clientIp);

            // Track connection event in Application Insights
            var connectionEvent = new EventTelemetry("SignalR.ConnectionEstablished");
            connectionEvent.Properties["ConnectionId"] = connectionId;
            connectionEvent.Properties["UserId"] = userId ?? "Unknown";
            connectionEvent.Properties["UserName"] = userName ?? "Anonymous";
            connectionEvent.Properties["HubName"] = hubName;
            connectionEvent.Properties["ClientIp"] = clientIp;
            connectionEvent.Properties["UserAgent"] = userAgent ?? "Unknown";
            connectionEvent.Properties["CorrelationId"] = correlationId;
            _telemetryClient.TrackEvent(connectionEvent);

            // Track connection metrics
            _telemetryClient.TrackMetric($"SignalR.{hubName}.Connections", 1);
            _telemetryClient.TrackMetric("SignalR.TotalConnections", _connections.Count);
        }
    }

    /// <summary>
    /// Logs SignalR connection termination
    /// </summary>
    public async Task LogDisconnectionAsync(HubCallerContext context, string hubName, Exception? exception)
    {
        var connectionId = context.ConnectionId;
        var correlationId = GetCorrelationId(context.GetHttpContext());

        if (_connections.TryRemove(connectionId, out var connectionInfo))
        {
            var duration = DateTime.UtcNow - connectionInfo.ConnectedAt;

            using (LogContext.PushProperty("CorrelationId", correlationId))
            using (LogContext.PushProperty("ConnectionId", connectionId))
            using (LogContext.PushProperty("UserId", connectionInfo.UserId))
            using (LogContext.PushProperty("UserName", connectionInfo.UserName))
            using (LogContext.PushProperty("HubName", hubName))
            using (LogContext.PushProperty("ClientIp", connectionInfo.ClientIp))
            using (LogContext.PushProperty("SignalREvent", "CONNECTION_TERMINATED"))
            {
                if (exception != null)
                {
                    _logger.LogWarning(exception, "SignalR connection terminated with error: User {UserName} ({UserId}) disconnected from {HubName} after {Duration}ms",
                        connectionInfo.UserName ?? "Anonymous", connectionInfo.UserId ?? "Unknown", hubName, duration.TotalMilliseconds);
                }
                else
                {
                    _logger.LogInformation("SignalR connection terminated: User {UserName} ({UserId}) disconnected from {HubName} after {Duration}ms",
                        connectionInfo.UserName ?? "Anonymous", connectionInfo.UserId ?? "Unknown", hubName, duration.TotalMilliseconds);
                }

                // Track disconnection event in Application Insights
                var disconnectionEvent = new EventTelemetry("SignalR.ConnectionTerminated");
                disconnectionEvent.Properties["ConnectionId"] = connectionId;
                disconnectionEvent.Properties["UserId"] = connectionInfo.UserId ?? "Unknown";
                disconnectionEvent.Properties["UserName"] = connectionInfo.UserName ?? "Anonymous";
                disconnectionEvent.Properties["HubName"] = hubName;
                disconnectionEvent.Properties["ClientIp"] = connectionInfo.ClientIp;
                disconnectionEvent.Properties["Duration"] = duration.TotalMilliseconds.ToString();
                disconnectionEvent.Properties["HasError"] = (exception != null).ToString();
                disconnectionEvent.Properties["CorrelationId"] = correlationId;
                
                if (exception != null)
                {
                    disconnectionEvent.Properties["ExceptionType"] = exception.GetType().Name;
                    disconnectionEvent.Properties["ExceptionMessage"] = exception.Message;
                }

                _telemetryClient.TrackEvent(disconnectionEvent);

                // Track connection duration metrics
                _telemetryClient.TrackMetric($"SignalR.{hubName}.ConnectionDuration", duration.TotalMilliseconds);
                _telemetryClient.TrackMetric("SignalR.TotalConnections", _connections.Count);
            }
        }
    }

    /// <summary>
    /// Logs SignalR method invocation
    /// </summary>
    public async Task LogMethodInvocationAsync(HubCallerContext context, string hubName, string methodName, object?[] args)
    {
        var connectionId = context.ConnectionId;
        var correlationId = GetCorrelationId(context.GetHttpContext());
        var stopwatch = Stopwatch.StartNew();

        if (_connections.TryGetValue(connectionId, out var connectionInfo))
        {
            using (LogContext.PushProperty("CorrelationId", correlationId))
            using (LogContext.PushProperty("ConnectionId", connectionId))
            using (LogContext.PushProperty("UserId", connectionInfo.UserId))
            using (LogContext.PushProperty("UserName", connectionInfo.UserName))
            using (LogContext.PushProperty("HubName", hubName))
            using (LogContext.PushProperty("MethodName", methodName))
            using (LogContext.PushProperty("SignalREvent", "METHOD_INVOCATION"))
            {
                _logger.LogDebug("SignalR method invoked: {UserName} ({UserId}) called {MethodName} on {HubName}",
                    connectionInfo.UserName ?? "Anonymous", connectionInfo.UserId ?? "Unknown", methodName, hubName);

                // Track method invocation event
                var methodEvent = new EventTelemetry("SignalR.MethodInvoked");
                methodEvent.Properties["ConnectionId"] = connectionId;
                methodEvent.Properties["UserId"] = connectionInfo.UserId ?? "Unknown";
                methodEvent.Properties["UserName"] = connectionInfo.UserName ?? "Anonymous";
                methodEvent.Properties["HubName"] = hubName;
                methodEvent.Properties["MethodName"] = methodName;
                methodEvent.Properties["ArgumentCount"] = args?.Length.ToString() ?? "0";
                methodEvent.Properties["CorrelationId"] = correlationId;
                _telemetryClient.TrackEvent(methodEvent);

                // Track method invocation metrics
                _telemetryClient.TrackMetric($"SignalR.{hubName}.{methodName}.Invocations", 1);
            }
        }
    }

    /// <summary>
    /// Logs SignalR message sending
    /// </summary>
    public async Task LogMessageSentAsync(string connectionId, string hubName, string methodName, object? data, string? targetGroup = null)
    {
        var messageInfo = new MessageInfo
        {
            ConnectionId = connectionId,
            HubName = hubName,
            MethodName = methodName,
            TargetGroup = targetGroup,
            SentAt = DateTime.UtcNow,
            DataSize = EstimateDataSize(data)
        };

        // Store recent messages for analysis
        var connectionMessages = _recentMessages.GetOrAdd(connectionId, _ => new List<MessageInfo>());
        lock (connectionMessages)
        {
            connectionMessages.Add(messageInfo);
            // Keep only last 100 messages per connection
            if (connectionMessages.Count > 100)
            {
                connectionMessages.RemoveAt(0);
            }
        }

        if (_connections.TryGetValue(connectionId, out var connectionInfo))
        {
            using (LogContext.PushProperty("ConnectionId", connectionId))
            using (LogContext.PushProperty("UserId", connectionInfo.UserId))
            using (LogContext.PushProperty("UserName", connectionInfo.UserName))
            using (LogContext.PushProperty("HubName", hubName))
            using (LogContext.PushProperty("MethodName", methodName))
            using (LogContext.PushProperty("TargetGroup", targetGroup))
            using (LogContext.PushProperty("SignalREvent", "MESSAGE_SENT"))
            {
                _logger.LogDebug("SignalR message sent: {MethodName} to {Target} on {HubName} (Size: {DataSize} bytes)",
                    methodName, targetGroup ?? "caller", hubName, messageInfo.DataSize);

                // Track message sent event
                var messageEvent = new EventTelemetry("SignalR.MessageSent");
                messageEvent.Properties["ConnectionId"] = connectionId;
                messageEvent.Properties["UserId"] = connectionInfo.UserId ?? "Unknown";
                messageEvent.Properties["UserName"] = connectionInfo.UserName ?? "Anonymous";
                messageEvent.Properties["HubName"] = hubName;
                messageEvent.Properties["MethodName"] = methodName;
                messageEvent.Properties["TargetGroup"] = targetGroup ?? "caller";
                messageEvent.Properties["DataSize"] = messageInfo.DataSize.ToString();
                _telemetryClient.TrackEvent(messageEvent);

                // Track message metrics
                _telemetryClient.TrackMetric($"SignalR.{hubName}.{methodName}.MessagesSent", 1);
                _telemetryClient.TrackMetric($"SignalR.{hubName}.MessageSize", messageInfo.DataSize);
            }
        }
    }

    /// <summary>
    /// Gets current connection statistics
    /// </summary>
    public ConnectionStatistics GetConnectionStatistics()
    {
        var stats = new ConnectionStatistics
        {
            TotalConnections = _connections.Count,
            ConnectionsByHub = _connections.Values.GroupBy(c => c.HubName).ToDictionary(g => g.Key, g => g.Count()),
            ConnectionsByUser = _connections.Values.Where(c => !string.IsNullOrEmpty(c.UserId)).GroupBy(c => c.UserId).ToDictionary(g => g.Key!, g => g.Count()),
            AverageConnectionDuration = _connections.Values.Any() ? 
                _connections.Values.Average(c => (DateTime.UtcNow - c.ConnectedAt).TotalMinutes) : 0
        };

        // Track real-time connection metrics
        _telemetryClient.TrackMetric("SignalR.ActiveConnections", stats.TotalConnections);
        foreach (var hubStat in stats.ConnectionsByHub)
        {
            _telemetryClient.TrackMetric($"SignalR.{hubStat.Key}.ActiveConnections", hubStat.Value);
        }

        return stats;
    }

    private string GetClientIpAddress(HttpContext? context)
    {
        if (context == null) return "Unknown";

        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }

    private string GetCorrelationId(HttpContext? context)
    {
        if (context?.Request.Headers.TryGetValue("X-Correlation-ID", out var correlationId) == true)
        {
            return correlationId.FirstOrDefault() ?? Guid.NewGuid().ToString();
        }
        return Guid.NewGuid().ToString();
    }

    private int EstimateDataSize(object? data)
    {
        if (data == null) return 0;
        
        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(data);
            return System.Text.Encoding.UTF8.GetByteCount(json);
        }
        catch
        {
            return data.ToString()?.Length ?? 0;
        }
    }

    private record ConnectionInfo
    {
        public string ConnectionId { get; init; } = string.Empty;
        public string? UserId { get; init; }
        public string? UserName { get; init; }
        public string HubName { get; init; } = string.Empty;
        public string ClientIp { get; init; } = string.Empty;
        public string? UserAgent { get; init; }
        public DateTime ConnectedAt { get; init; }
        public string CorrelationId { get; init; } = string.Empty;
    }

    private record MessageInfo
    {
        public string ConnectionId { get; init; } = string.Empty;
        public string HubName { get; init; } = string.Empty;
        public string MethodName { get; init; } = string.Empty;
        public string? TargetGroup { get; init; }
        public DateTime SentAt { get; init; }
        public int DataSize { get; init; }
    }

    public record ConnectionStatistics
    {
        public int TotalConnections { get; init; }
        public Dictionary<string, int> ConnectionsByHub { get; init; } = new();
        public Dictionary<string, int> ConnectionsByUser { get; init; } = new();
        public double AverageConnectionDuration { get; init; }
    }
}
