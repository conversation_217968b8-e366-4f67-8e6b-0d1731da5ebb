@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

/* Custom FleetXQ styles */
html {
  font-family: 'Inter', system-ui, sans-serif;
}

body {
  background-color: rgb(249 250 251);
  color: rgb(17 24 39);
}

/* Component styles */
.btn-primary {
  background-color: rgb(37 99 235);
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: rgb(29 78 216);
}

.btn-secondary {
  background-color: rgb(241 245 249);
  color: rgb(51 65 85);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: rgb(226 232 240);
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  border: 1px solid rgb(229 231 235);
  padding: 1.5rem;
}

.input-field {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgb(209 213 219);
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.input-field:focus {
  border-color: rgb(59 130 246);
  box-shadow: 0 0 0 2px rgb(59 130 246 / 0.5);
}

.sidebar-nav-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: background-color 0.2s, color 0.2s;
}

.sidebar-nav-item-active {
  background-color: rgb(219 234 254);
  color: rgb(29 78 216);
}

.sidebar-nav-item-inactive {
  color: rgb(75 85 99);
}

.sidebar-nav-item-inactive:hover {
  background-color: rgb(249 250 251);
  color: rgb(17 24 39);
}
