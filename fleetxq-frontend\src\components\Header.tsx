import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { UserIcon } from '@heroicons/react/24/outline';

const Header = () => {
  const { user } = useSelector((state: RootState) => state.auth);

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center">
          <h2 className="text-lg font-semibold text-gray-900">
            Fleet Management Dashboard
          </h2>
        </div>

        <div className="flex items-center space-x-4">
          {/* User info */}
          <div className="flex items-center space-x-2">
            <UserIcon className="w-5 h-5 text-gray-400" />
            <span className="text-sm font-medium text-gray-700">
              {user?.name || 'User'}
            </span>
            <span className="text-xs text-gray-500 capitalize">
              ({user?.role || 'guest'})
            </span>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
